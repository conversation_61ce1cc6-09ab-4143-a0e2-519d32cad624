import spacy
import os
import time
import pandas as pd
from .embeddings_management import EmbeddingManager
from .csv_converter import csv_to_json


def init_models(app_or_settings):
    """Initialize language models and the default label pool's vector database.

    Args:
        app_or_settings: Either a Flask app object (for backward compatibility)
                        or a Pydantic Settings object (for FastAPI)
    """
    start = time.perf_counter()

    # Handle both Flask app and Pydantic settings
    if hasattr(app_or_settings, 'config'):
        # Flask app object
        config = app_or_settings.config
        st_model_name = config['ST_MODEL_NAME']
        nlp_spacy_name = config['NLP_SPACY_NAME']
        label_pool_file = config['LABEL_POOL_FILE']
        default_collection_name = config['DEFAULT_COLLECTION_NAME']
    else:
        # Pydantic settings object
        st_model_name = app_or_settings.st_model_name
        nlp_spacy_name = app_or_settings.nlp_spacy_name
        label_pool_file = app_or_settings.label_pool_file
        default_collection_name = app_or_settings.default_collection_name

    # Load Sentence Transformer model and Spacy NLP model
    current_directory = os.path.dirname(os.path.abspath(__file__))
    chroma_persist_dir = os.path.join(current_directory, "chroma_db")
    embeddings_manager = EmbeddingManager(persist_directory=chroma_persist_dir, embedding_model=st_model_name)
    nlp_spacy = spacy.load(nlp_spacy_name)
    end = time.perf_counter()
    print(f"Time for initializing language models: {end - start:.4f} seconds")

    start = time.perf_counter()
    print("Initializing label pool...")
    # Read label data from csv file and convert to json
    label_pool_path = os.path.join(current_directory, label_pool_file)
    label_pool_data = csv_to_json(label_pool_path, delimiter='|', has_header='yes')
    label_pool_data_df = pd.DataFrame(label_pool_data)
    label_pool_name_set = set(label_pool_data_df['name'])

    # Create a collection, upsert data if the collection was newly created, and load the collection into memory
    res = embeddings_manager.create_collection(collection_name=default_collection_name)
    if res == "success":
        print("Database and collection created. Started to compute and upsert embeddings.")
        embeddings_manager.upsert_data(collection_name=default_collection_name, data=label_pool_data)
    embeddings_manager.load_collection(collection_name=default_collection_name)
    print("Label pool and its embeddings initialized.")

    end = time.perf_counter()
    print(f"Time for initialization: {end - start:.4f} seconds")

    return embeddings_manager, nlp_spacy, label_pool_data, label_pool_name_set