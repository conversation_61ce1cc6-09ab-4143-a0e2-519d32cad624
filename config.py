import os
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Base settings using Pydantic BaseSettings for FastAPI."""

    # Application paths
    app_root: str = os.path.dirname(os.path.abspath(__file__))
    core_dir: str = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'core')

    # Model configuration
    st_model_name: str = "all-mpnet-base-v2"
    nlp_spacy_name: str = "en_core_web_md"
    label_pool_file: str = "default_label_pool.csv"

    # Environment configuration
    environment: str = "production"
    debug: bool = False
    testing: bool = False

    # ChromaDB configuration
    default_collection_name: str = "default_collection"
    chroma_persist_directory: str = "./app/core/chroma_db"

    # API configuration
    api_title: str = "Academic Article Tagger API"
    api_description: str = "API for automatic tagging of academic articles using NLP and ML models"
    api_version: str = "1.0.0"

    # CORS configuration
    cors_origins: list = ["http://localhost:5173"]

    # OpenAI configuration
    openai_api_key: str = ""

    class Config:
        env_file = ".env"
        case_sensitive = False


class ProductionSettings(Settings):
    """Production settings."""
    environment: str = "production"
    debug: bool = False
    testing: bool = False
    default_collection_name: str = "default_collection"


class DevelopmentSettings(Settings):
    """Development settings."""
    environment: str = "development"
    debug: bool = True
    testing: bool = False
    default_collection_name: str = "test_collection"


class TestingSettings(Settings):
    """Testing settings."""
    environment: str = "testing"
    debug: bool = True
    testing: bool = True
    default_collection_name: str = "test_collection"


# For backward compatibility with Flask code
Config = Settings
ProductionConfig = ProductionSettings
DevelopmentConfig = DevelopmentSettings
TestingConfig = TestingSettings